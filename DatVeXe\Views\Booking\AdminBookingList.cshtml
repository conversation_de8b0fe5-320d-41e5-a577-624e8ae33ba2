@model IEnumerable<dynamic>
@{
    Layout = "~/Areas/Admin/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Quản lý đơn đặt vé";
    var chuyenXeList = ViewBag.ChuyenXeList as List<DatVeXe.Models.ChuyenXe>;
    var trangThaiVeList = ViewBag.TrangThaiVeList as List<DatVeXe.Models.TrangThaiVe>;
    string selectedChuyenXeId = ViewBag.SelectedChuyenXeId as string ?? "";
    string selectedNgayDi = ViewBag.SelectedNgayDi as string ?? "";
}

@Html.AntiForgeryToken()
    string selectedTrangThaiVe = ViewBag.SelectedTrangThaiVe as string ?? "";
    string selectedTenKhach = ViewBag.SelectedTenKhach as string ?? "";

    var totalVe = Model != null ? Model.Count() : 0;
    var daThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 2) : 0;
    var chuaThanhToanCount = Model != null ? Model.Count(v => (int)v.TrangThai == 1) : 0;
    var daHuyCount = Model != null ? Model.Count(v => (int)v.TrangThai == 4) : 0;
}

<style>
    .info-box {
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        border-radius: .25rem;
        background-color: #fff;
        display: flex;
        margin-bottom: 1rem;
        min-height: 80px;
        padding: .5rem;
        position: relative;
        width: 100%;
    }

    .info-box .info-box-icon {
        border-radius: .25rem;
        align-items: center;
        display: flex;
        font-size: 1.875rem;
        justify-content: center;
        text-align: center;
        width: 70px;
        color: rgba(255,255,255,.8);
        flex-shrink: 0;
    }

    .info-box .info-box-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        line-height: 1.8;
        margin-left: .5rem;
        padding: 0 .5rem;
        flex: 1;
    }

    .info-box .info-box-text {
        display: block;
        font-size: .875rem;
        font-weight: 600;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .info-box .info-box-number {
        display: block;
        margin-top: .25rem;
        font-weight: 700;
    }

    .info-box .progress {
        background-color: rgba(0,0,0,.125);
        height: 2px;
        margin: 5px 0;
    }

    .info-box .progress-description {
        color: #6c757d;
        font-size: .75rem;
        margin: 0;
    }

    .elevation-1 {
        box-shadow: 0 1px 3px rgba(0,0,0,.12), 0 1px 2px rgba(0,0,0,.24);
    }

    .badge-lg {
        font-size: 0.9rem !important;
        padding: 8px 12px !important;
        font-weight: bold !important;
        border-radius: 6px !important;
    }

    .table td {
        vertical-align: middle !important;
    }

    .card {
        box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
        border: 0;
        border-radius: .25rem;
    }

    .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0,0,0,.125);
        padding: .75rem 1.25rem;
        position: relative;
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem;
    }
</style>

<!-- Content Wrapper -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0 text-dark">
                        <i class="fas fa-ticket-alt text-primary"></i>
                        Quản lý đơn đặt vé
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/Admin" class="text-dark">Admin</a></li>
                        <li class="breadcrumb-item active text-dark">Quản lý đặt vé</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Buttons -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="btn-group">
                        <a href="@Url.Action("QRScanner", "Booking")" class="btn btn-warning">
                            <i class="fas fa-qrcode"></i> Quét QR
                        </a>
                        <a href="@Url.Action("BookingStatistics", "Booking")" class="btn btn-info">
                            <i class="fas fa-chart-bar"></i> Thống kê
                        </a>
                        @if (!string.IsNullOrEmpty(selectedChuyenXeId))
                        {
                            <a href="@Url.Action("ExportPassengerList", "Booking", new { chuyenXeId = selectedChuyenXeId })" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Xuất Excel danh sách hành khách
                            </a>
                        }
                    </div>
                </div>
            </div>
            <!-- Dashboard Stats -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="info-box">
                        <span class="info-box-icon bg-info elevation-1">
                            <i class="fas fa-ticket-alt"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text text-dark font-weight-bold">Tổng đơn đặt vé</span>
                            <span class="info-box-number text-dark">
                                <h3 class="text-primary font-weight-bold mb-0">@totalVe</h3>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 100%"></div>
                            </div>
                            <span class="progress-description text-muted">
                                <i class="fas fa-chart-line"></i> Tổng số vé hiện tại
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="info-box">
                        <span class="info-box-icon bg-success elevation-1">
                            <i class="fas fa-check-circle"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text text-dark font-weight-bold">Đã thanh toán</span>
                            <span class="info-box-number text-dark">
                                <h3 class="text-success font-weight-bold mb-0">@daThanhToanCount</h3>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: @(totalVe > 0 ? daThanhToanCount * 100 / totalVe : 0)%"></div>
                            </div>
                            <span class="progress-description text-muted">
                                <i class="fas fa-percentage"></i> @(totalVe > 0 ? $"{daThanhToanCount * 100 / totalVe}%" : "0%") tỷ lệ thanh toán
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="info-box">
                        <span class="info-box-icon bg-warning elevation-1">
                            <i class="fas fa-exclamation-triangle"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text text-dark font-weight-bold">Chưa thanh toán</span>
                            <span class="info-box-number text-dark">
                                <h3 class="text-warning font-weight-bold mb-0">@chuaThanhToanCount</h3>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: @(totalVe > 0 ? chuaThanhToanCount * 100 / totalVe : 0)%"></div>
                            </div>
                            <span class="progress-description text-muted">
                                <i class="fas fa-clock"></i> @(totalVe > 0 ? $"{chuaThanhToanCount * 100 / totalVe}%" : "0%") cần xử lý
                            </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-6">
                    <div class="info-box">
                        <span class="info-box-icon bg-danger elevation-1">
                            <i class="fas fa-times-circle"></i>
                        </span>
                        <div class="info-box-content">
                            <span class="info-box-text text-dark font-weight-bold">Đã hủy</span>
                            <span class="info-box-number text-dark">
                                <h3 class="text-danger font-weight-bold mb-0">@daHuyCount</h3>
                            </span>
                            <div class="progress">
                                <div class="progress-bar bg-danger" style="width: @(totalVe > 0 ? daHuyCount * 100 / totalVe : 0)%"></div>
                            </div>
                            <span class="progress-description text-muted">
                                <i class="fas fa-ban"></i> @(totalVe > 0 ? $"{daHuyCount * 100 / totalVe}%" : "0%") tỷ lệ hủy vé
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Card -->
            <div class="row">
                <div class="col-12">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title text-dark">
                                <i class="fas fa-filter"></i> Bộ lọc tìm kiếm
                            </h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-tool btn-sm" onclick="toggleAdvancedFilter()">
                                    <i class="fas fa-cog"></i> Bộ lọc nâng cao
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="get" id="filterForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="text-dark font-weight-bold">
                                                <i class="fas fa-calendar text-primary"></i>
                                                Ngày đi
                                            </label>
                                            <input type="date" name="ngayDi" class="form-control" value="@selectedNgayDi" />
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="text-dark font-weight-bold">
                                                <i class="fas fa-bus text-primary"></i>
                                                Chuyến xe
                                            </label>
                                            <select name="chuyenXeId" class="form-control">
                                                <option value="">-- Chọn chuyến xe --</option>
                                                @if (chuyenXeList != null)
                                                {
                                                    foreach (var cx in chuyenXeList)
                                                    {
                                                        var tenTuyen = cx.TuyenDuong != null ? cx.TuyenDuong.TenTuyen : ($"{cx.DiemDi} - {cx.DiemDen}");
                                                        if (selectedChuyenXeId == cx.ChuyenXeId.ToString())
                                                        {
                                                            <option value="@cx.ChuyenXeId" selected>@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="@cx.ChuyenXeId">@tenTuyen (@cx.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm"))</option>
                                                        }
                                                    }
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="text-dark font-weight-bold">
                                                <i class="fas fa-tag text-primary"></i>
                                                Trạng thái vé
                                            </label>
                                            <select name="trangThaiVe" class="form-control">
                                                <option value="">-- Trạng thái vé --</option>
                                                @if (trangThaiVeList != null)
                                                {
                                                    foreach (var t in trangThaiVeList)
                                                    {
                                                        if (selectedTrangThaiVe == ((int)t).ToString())
                                                        {
                                                            <option value="@((int)t)" selected>@t</option>
                                                        }
                                                        else
                                                        {
                                                            <option value="@((int)t)">@t</option>
                                                        }
                                                    }
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="text-dark font-weight-bold">
                                                <i class="fas fa-user text-primary"></i>
                                                Tên khách
                                            </label>
                                            <input type="text" name="tenKhach" class="form-control" value="@selectedTenKhach" placeholder="Tên khách" />
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="text-dark font-weight-bold">&nbsp;</label>
                                            <button type="button" class="btn btn-primary btn-block" onclick="validateAndSubmitFilter()">
                                                <i class="fas fa-search"></i> Tìm kiếm
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Filters -->
                                <div class="row mb-2">
                                    <div class="col-12">
                                        <div class="d-flex flex-wrap gap-2">
                                            <small class="text-dark font-weight-bold align-self-center mr-2">Lọc nhanh:</small>
                                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="quickFilterByStatus('1')">
                                                <i class="fas fa-exclamation-circle"></i> Đã đặt
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-success" onclick="quickFilterByStatus('2')">
                                                <i class="fas fa-check-circle"></i> Đã thanh toán
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="quickFilterByStatus('5')">
                                                <i class="fas fa-user-check"></i> Đã sử dụng
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="quickFilterByStatus('4')">
                                                <i class="fas fa-times-circle"></i> Đã hủy
                                            </button>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                                    <i class="fas fa-calendar"></i> Thời gian
                                                </button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="#" onclick="quickFilterByDateRange(1)">Hôm nay</a>
                                                    <a class="dropdown-item" href="#" onclick="quickFilterByDateRange(7)">7 ngày qua</a>
                                                    <a class="dropdown-item" href="#" onclick="quickFilterByDateRange(30)">30 ngày qua</a>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="resetAllFilters()">
                                                <i class="fas fa-redo"></i> Đặt lại
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Advanced filters (initially hidden) -->
                                <div id="advancedFilter" style="display: none;">
                                    <hr class="my-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-calendar-plus text-primary"></i>
                                                    Từ ngày đặt
                                                </label>
                                                <input type="date" name="tuNgay" class="form-control" value="@ViewBag.TuNgayFilter" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-calendar-minus text-primary"></i>
                                                    Đến ngày đặt
                                                </label>
                                                <input type="date" name="denNgay" class="form-control" value="@ViewBag.DenNgayFilter" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-phone text-success"></i>
                                                    Số điện thoại
                                                </label>
                                                <input type="text" name="soDienThoai" class="form-control" value="@ViewBag.SoDienThoaiFilter" placeholder="Nhập số điện thoại" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-envelope text-info"></i>
                                                    Email
                                                </label>
                                                <input type="email" name="email" class="form-control" value="@ViewBag.EmailFilter" placeholder="Nhập email" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-ticket-alt text-warning"></i>
                                                    Mã vé
                                                </label>
                                                <input type="text" name="maVe" class="form-control" value="@ViewBag.MaVeFilter" placeholder="Nhập mã vé" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-money-bill text-success"></i>
                                                    Giá vé từ (VNĐ)
                                                </label>
                                                <input type="number" name="giaVeMin" class="form-control" value="@ViewBag.GiaVeMinFilter" placeholder="0" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-money-bill-wave text-success"></i>
                                                    Giá vé đến (VNĐ)
                                                </label>
                                                <input type="number" name="giaVeMax" class="form-control" value="@ViewBag.GiaVeMaxFilter" placeholder="1000000" />
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="text-dark font-weight-bold">
                                                    <i class="fas fa-credit-card text-primary"></i>
                                                    Trạng thái thanh toán
                                                </label>
                                                <select name="trangThaiThanhToan" class="form-control">
                                                    <option value="">-- Tất cả --</option>
                                                    @{
                                                        var trangThaiThanhToanFilter = ViewBag.TrangThaiThanhToanFilter as string;
                                                    }
                                                    <option value="true" selected="@(trangThaiThanhToanFilter == "true")">Đã thanh toán</option>
                                                    <option value="false" selected="@(trangThaiThanhToanFilter == "false")">Chưa thanh toán</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-success" onclick="exportWithFilters('csv')">
                                                    <i class="fas fa-file-csv"></i> Xuất CSV
                                                </button>
                                                <button type="button" class="btn btn-info" onclick="exportWithFilters('excel')">
                                                    <i class="fas fa-file-excel"></i> Xuất Excel
                                                </button>
                                                <button type="button" class="btn btn-secondary" onclick="resetAllFilters()">
                                                    <i class="fas fa-redo"></i> Đặt lại tất cả
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title text-dark">
                                <i class="fas fa-list"></i> Danh sách đơn đặt vé
                            </h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 300px;">
                                    <input type="text" id="searchTickets" class="form-control" placeholder="Tìm kiếm theo mã vé, tên khách, SĐT..." />
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-secondary" id="clearSearch" title="Xóa tìm kiếm">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <button type="button" class="btn btn-primary" title="Tìm kiếm">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-1">
                                    <small id="searchResultCount" class="text-muted"></small>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <button class="btn btn-warning" id="bulkUpdateBtn" onclick="showBulkUpdateModal()" disabled>
                                        <i class="fas fa-edit"></i> Cập nhật hàng loạt
                                    </button>
                                    <button class="btn btn-info ml-2" id="bulkNotificationBtn" onclick="showBulkNotificationModal()" disabled>
                                        <i class="fas fa-envelope"></i> Gửi thông báo hàng loạt
                                    </button>
                                    <button class="btn btn-success ml-2" id="bulkPrintBtn" onclick="bulkPrintTickets()" disabled>
                                        <i class="fas fa-print"></i> In vé hàng loạt
                                    </button>
                                </div>
                                <div class="col-md-6 text-right">
                                    <span class="badge badge-primary badge-lg">
                                        <i class="fas fa-ticket-alt"></i>
                                        @(Model != null ? Model.Count() : 0) đơn đặt vé
                                    </span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-striped" id="ticketsTable">
                                    <thead>
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                            </th>
                                            <th class="text-dark">Mã vé</th>
                                            <th class="text-dark">Tên khách</th>
                                            <th class="text-dark">SĐT</th>
                                            <th class="text-dark">Chuyến xe</th>
                                            <th class="text-dark">Ghế</th>
                                            <th class="text-dark">Trạng thái</th>
                                            <th class="text-dark">Ngày đặt</th>
                                            <th class="text-dark">Thao tác</th>
                                        </tr>
                                    </thead>
                    <tbody>
                        @if (Model != null && Model.Any())
                        {
                            foreach (var v in Model)
                            {
                                <tr data-search="@v.MaVe @v.TenKhach @v.SoDienThoai @v.ChuyenXe @v.Ghe">
                                    <td>
                                        <input type="checkbox" class="ticket-checkbox" value="@v.VeId" onchange="updateBulkActions()">
                                    </td>
                                    <td><span class="fw-medium">@v.MaVe</span></td>
                                    <td>@v.TenKhach</td>
                                    <td>@v.SoDienThoai</td>
                                    <td>@v.ChuyenXe</td>
                                    <td>@v.Ghe</td>
                                    <td>
                                        @{
                                            var ticketStatusClass = "";
                                            var ticketStatusText = "";
                                            var ticketStatusIcon = "";
                                            var ticketStatusStyle = "";
                                            var statusBgColor = "";
                                            var statusTextColor = "";

                                            switch ((int)v.TrangThai)
                                            {
                                                case 1: // DaDat - Đã đặt
                                                    ticketStatusClass = "badge badge-warning badge-lg ticket-status-pending";
                                                    ticketStatusText = "Đã đặt";
                                                    ticketStatusIcon = "fas fa-clock";
                                                    statusBgColor = "#ffc107";
                                                    statusTextColor = "#212529";
                                                    break;
                                                case 2: // DaThanhToan - Đã thanh toán
                                                    ticketStatusClass = "badge badge-success badge-lg ticket-status-paid";
                                                    ticketStatusText = "Đã thanh toán";
                                                    ticketStatusIcon = "fas fa-check-circle";
                                                    statusBgColor = "#28a745";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                                case 3: // DaHoanThanh - Đã hoàn thành
                                                    ticketStatusClass = "badge badge-primary badge-lg ticket-status-completed";
                                                    ticketStatusText = "Đã hoàn thành";
                                                    ticketStatusIcon = "fas fa-check-double";
                                                    statusBgColor = "#007bff";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                                case 4: // DaHuy - Đã hủy
                                                    ticketStatusClass = "badge badge-danger badge-lg ticket-status-cancelled";
                                                    ticketStatusText = "Đã hủy";
                                                    ticketStatusIcon = "fas fa-times-circle";
                                                    statusBgColor = "#dc3545";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                                case 5: // DaSuDung - Đã sử dụng
                                                    ticketStatusClass = "badge badge-info badge-lg ticket-status-used";
                                                    ticketStatusText = "Đã sử dụng";
                                                    ticketStatusIcon = "fas fa-user-check";
                                                    statusBgColor = "#17a2b8";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                                case 6: // DaHoanTien - Đã hoàn tiền
                                                    ticketStatusClass = "badge badge-dark badge-lg ticket-status-refunded";
                                                    ticketStatusText = "Đã hoàn tiền";
                                                    ticketStatusIcon = "fas fa-undo-alt";
                                                    statusBgColor = "#6c757d";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                                default:
                                                    ticketStatusClass = "badge badge-secondary badge-lg ticket-status-unknown";
                                                    ticketStatusText = "Không xác định";
                                                    ticketStatusIcon = "fas fa-question-circle";
                                                    statusBgColor = "#6c757d";
                                                    statusTextColor = "#ffffff";
                                                    break;
                                            }
                                            ticketStatusStyle = $"background-color: {statusBgColor}; color: {statusTextColor}; font-size: 0.85rem; padding: 8px 14px; font-weight: 600; border-radius: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: none; text-transform: uppercase; letter-spacing: 0.5px;";
                                        }
                                        <div class="ticket-status-container">
                                            <span class="@ticketStatusClass ticket-status-enhanced" style="@ticketStatusStyle" data-status="@((int)v.TrangThai)">
                                                <i class="@ticketStatusIcon mr-1"></i>@ticketStatusText
                                            </span>
                                            <div class="status-quick-actions mt-1">
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-outline-secondary btn-xs status-change-btn"
                                                            data-ve-id="@v.VeId" data-current-status="@((int)v.TrangThai)"
                                                            title="Thay đổi trạng thái">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>@(v.NgayDat.ToString("dd/MM/yyyy HH:mm"))</td>
                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                <i class="fas fa-cog"></i>
                                                            </button>
                                                            <div class="dropdown-menu dropdown-menu-right">
                                                                <a class="dropdown-item" href="@Url.Action("AdminBookingDetail", "Booking", new { id = v.VeId })">
                                                                    <i class="fas fa-eye text-primary"></i> Xem chi tiết
                                                                </a>
                                                                <a class="dropdown-item print-ticket-options" href="#" data-id="@v.VeId" data-ma-ve="@v.MaVe">
                                                                    <i class="fas fa-print text-info"></i> In vé
                                                                </a>
                                                                <div class="dropdown-divider"></div>
                                                                @if ((int)v.TrangThai == 2) // Đã thanh toán
                                                                {
                                                                    <a class="dropdown-item check-in-passenger" href="#" data-id="@v.VeId" data-ma-ve="@v.MaVe" data-ten-khach="@v.TenKhach">
                                                                        <i class="fas fa-user-check text-success"></i> Check-in hành khách
                                                                    </a>
                                                                }
                                                                <a class="dropdown-item" href="#" onclick="updateTripStatus(@v.VeId, 'DaDon'); return false;">
                                                                    <i class="fas fa-check text-success"></i> Đã đón
                                                                </a>
                                                                <a class="dropdown-item text-warning" href="#" onclick="updateTripStatus(@v.VeId, 'KhongCoMat'); return false;">
                                                                    <i class="fas fa-times text-warning"></i> Không có mặt
                                                                </a>
                                                                <a class="dropdown-item text-danger" href="#" onclick="updateTripStatus(@v.VeId, 'HuyChuyen'); return false;">
                                                                    <i class="fas fa-ban text-danger"></i> Hủy chuyến
                                                                </a>
                                                                <div class="dropdown-divider"></div>
                                                                @if ((int)v.TrangThai == 4) // Đã hủy
                                                                {
                                                                    <a class="dropdown-item refund-ticket" href="#" data-id="@v.VeId" data-ma-ve="@v.MaVe">
                                                                        <i class="fas fa-money-bill-wave text-success"></i> Hoàn tiền
                                                                    </a>
                                                                }
                                                                @if ((int)v.TrangThai == 2 || (int)v.TrangThai == 1) // Đã thanh toán hoặc Đã đặt
                                                                {
                                                                    <a class="dropdown-item transfer-ticket" href="#" data-id="@v.VeId" data-ma-ve="@v.MaVe">
                                                                        <i class="fas fa-exchange-alt text-warning"></i> Chuyển vé
                                                                    </a>
                                                                }
                                                                <a class="dropdown-item add-note" href="#" data-id="@v.VeId">
                                                                    <i class="fas fa-sticky-note text-info"></i> Thêm ghi chú
                                                                </a>
                                                                <a class="dropdown-item send-notification" href="#" data-id="@v.VeId" data-name="@v.TenKhach">
                                                                    <i class="fas fa-envelope text-primary"></i> Gửi thông báo
                                                                </a>
                                                                <div class="dropdown-divider"></div>
                                                                <a class="dropdown-item view-history" href="#" data-id="@v.VeId">
                                                                    <i class="fas fa-history text-secondary"></i> Lịch sử thay đổi
                                                                </a>
                                                            </div>
                                                        </div>
                                    </td>
                                </tr>
                            }
                                            }
                                        }
                                        else
                                        {
                                            <tr>
                                                <td colspan="9" class="text-center py-5">
                                                    <div class="text-center">
                                                        <i class="fas fa-inbox text-muted" style="font-size: 3rem;"></i>
                                                        <p class="mt-3 mb-0 text-dark">Không có dữ liệu</p>
                                                        <p class="text-muted">Hãy thử thay đổi bộ lọc tìm kiếm</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal thêm ghi chú -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">Thêm ghi chú</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="noteVeId" value="">
                <div class="form-group">
                    <label for="noteContent" class="text-dark font-weight-bold">Ghi chú</label>
                    <textarea class="form-control" id="noteContent" rows="4" placeholder="Nhập ghi chú..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="saveNote">Lưu ghi chú</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo -->
<div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">Gửi thông báo khách hàng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="notificationVeId" value="">

                <!-- Customer Info -->
                <div class="alert alert-info">
                    <strong>Thông tin khách hàng:</strong><br>
                    <span id="customerInfo">Đang tải...</span>
                </div>

                <!-- Template Selection -->
                <div class="form-group">
                    <label for="templateSelect" class="text-dark font-weight-bold">Chọn mẫu thông báo</label>
                    <select class="form-control" id="templateSelect" onchange="loadTemplate()">
                        <option value="">-- Chọn mẫu thông báo --</option>
                        <option value="confirm">Xác nhận đặt vé</option>
                        <option value="payment_success">Thanh toán thành công</option>
                        <option value="departure_reminder">Nhắc nhở khởi hành</option>
                        <option value="schedule_change">Thay đổi lịch trình</option>
                        <option value="cancellation">Hủy vé</option>
                        <option value="custom">Tùy chỉnh</option>
                    </select>
                </div>

                <!-- Notification Type -->
                <div class="form-group">
                    <label class="text-dark font-weight-bold">Phương thức gửi</label>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="sendEmail" checked>
                        <label class="custom-control-label text-dark" for="sendEmail">
                            <i class="fas fa-envelope"></i> Gửi Email
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="sendSMS">
                        <label class="custom-control-label text-dark" for="sendSMS">
                            <i class="fas fa-phone"></i> Gửi SMS
                        </label>
                    </div>
                </div>

                <!-- Subject -->
                <div class="form-group">
                    <label for="notificationSubject" class="text-dark font-weight-bold">Tiêu đề</label>
                    <input type="text" class="form-control" id="notificationSubject" placeholder="Tiêu đề thông báo">
                </div>

                <!-- Message -->
                <div class="form-group">
                    <label for="notificationMessage" class="text-dark font-weight-bold">Nội dung</label>
                    <textarea class="form-control" id="notificationMessage" rows="6" placeholder="Nội dung thông báo..."></textarea>
                    <small class="form-text text-muted">
                        Có thể sử dụng các biến: {TenKhach}, {MaVe}, {SoDienThoai}, {NgayKhoiHanh}, {TuyenDuong}, {SoGhe}
                    </small>
                </div>

                <!-- Preview -->
                <div class="form-group">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="previewMessage()">
                        <i class="fas fa-eye"></i> Xem trước
                    </button>
                </div>
                <div id="messagePreview" class="alert alert-light" style="display: none;">
                    <strong>Xem trước:</strong>
                    <div id="previewContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="sendNotification">
                    <i class="fas fa-paper-plane"></i> Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal thay đổi trạng thái đơn lẻ -->
<div class="modal fade" id="statusChangeModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-edit text-primary"></i>
                    Thay đổi trạng thái vé
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="statusChangeVeId" value="">
                <input type="hidden" id="statusChangeCurrentStatus" value="">

                <div class="alert alert-info">
                    <strong>Vé:</strong> <span id="statusChangeTicketInfo">Đang tải...</span>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-tag text-primary"></i>
                        Trạng thái hiện tại:
                    </label>
                    <div id="currentStatusDisplay" class="mb-2"></div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-arrow-right text-success"></i>
                        Chọn trạng thái mới:
                    </label>
                    <select class="form-control" id="newStatus">
                        <option value="">-- Chọn trạng thái --</option>
                        <option value="1" data-icon="fas fa-clock" data-color="#ffc107">Đã đặt</option>
                        <option value="2" data-icon="fas fa-check-circle" data-color="#28a745">Đã thanh toán</option>
                        <option value="3" data-icon="fas fa-check-double" data-color="#007bff">Đã hoàn thành</option>
                        <option value="4" data-icon="fas fa-times-circle" data-color="#dc3545">Đã hủy</option>
                        <option value="5" data-icon="fas fa-user-check" data-color="#17a2b8">Đã sử dụng</option>
                        <option value="6" data-icon="fas fa-undo-alt" data-color="#6c757d">Đã hoàn tiền</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-sticky-note text-info"></i>
                        Ghi chú (tùy chọn):
                    </label>
                    <textarea class="form-control" id="statusChangeNote" rows="3" placeholder="Nhập lý do thay đổi trạng thái..."></textarea>
                </div>

                <div class="alert alert-warning" id="statusChangeWarning" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="warningMessage"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-primary" id="executeStatusChange">
                    <i class="fas fa-save"></i> Cập nhật trạng thái
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal cập nhật hàng loạt -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">Cập nhật trạng thái hàng loạt</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="text-dark">Bạn đang cập nhật trạng thái cho <strong id="selectedCount">0</strong> vé.</p>
                <div class="form-group">
                    <label class="text-dark font-weight-bold">Chọn trạng thái mới:</label>
                    <select class="form-control" id="bulkStatus">
                        <option value="DaThanhToan">Đã thanh toán</option>
                        <option value="DaSuDung">Đã sử dụng</option>
                        <option value="DaHoanThanh">Đã hoàn thành</option>
                        <option value="DaHuy">Đã hủy</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="text-dark font-weight-bold">Ghi chú (tùy chọn):</label>
                    <textarea class="form-control" id="bulkNote" rows="3" placeholder="Nhập ghi chú..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-warning" id="executeBulkUpdate">Cập nhật</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal hoàn tiền vé -->
<div class="modal fade" id="refundModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-money-bill-wave text-success"></i>
                    Hoàn tiền vé
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="refundVeId" value="">

                <div class="alert alert-info">
                    <strong>Thông tin vé:</strong> <span id="refundTicketInfo">Đang tải...</span>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 text-dark">Thông tin thanh toán</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Giá vé gốc:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="originalPrice" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Phí hủy vé:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="cancellationFee" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Số tiền hoàn lại:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control font-weight-bold text-success" id="refundAmount" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 text-dark">Phương thức hoàn tiền</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Chọn phương thức:</label>
                                    <select class="form-control" id="refundMethod">
                                        <option value="">-- Chọn phương thức --</option>
                                        <option value="bank_transfer">Chuyển khoản ngân hàng</option>
                                        <option value="cash">Tiền mặt tại quầy</option>
                                        <option value="original_method">Hoàn về phương thức gốc</option>
                                    </select>
                                </div>
                                <div class="form-group" id="bankInfoGroup" style="display: none;">
                                    <label class="text-dark font-weight-bold">Thông tin ngân hàng:</label>
                                    <textarea class="form-control" id="bankInfo" rows="3" placeholder="Nhập số tài khoản, tên ngân hàng, tên chủ tài khoản..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label class="text-dark font-weight-bold">Lý do hoàn tiền:</label>
                    <textarea class="form-control" id="refundReason" rows="3" placeholder="Nhập lý do hoàn tiền..."></textarea>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Lưu ý:</strong> Việc hoàn tiền sẽ thay đổi trạng thái vé thành "Đã hoàn tiền" và không thể hoàn tác.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-success" id="executeRefund">
                    <i class="fas fa-money-bill-wave"></i> Thực hiện hoàn tiền
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal chuyển vé -->
<div class="modal fade" id="transferModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-exchange-alt text-warning"></i>
                    Chuyển vé
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="transferVeId" value="">

                <div class="alert alert-info">
                    <strong>Vé hiện tại:</strong> <span id="transferTicketInfo">Đang tải...</span>
                </div>

                <ul class="nav nav-tabs" id="transferTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="change-trip-tab" data-toggle="tab" href="#change-trip" role="tab">
                            <i class="fas fa-bus"></i> Đổi chuyến xe
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="change-passenger-tab" data-toggle="tab" href="#change-passenger" role="tab">
                            <i class="fas fa-user-edit"></i> Đổi thông tin hành khách
                        </a>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="transferTabContent">
                    <div class="tab-pane fade show active" id="change-trip" role="tabpanel">
                        <div class="form-group">
                            <label class="text-dark font-weight-bold">Chọn chuyến xe mới:</label>
                            <select class="form-control" id="newTripSelect">
                                <option value="">-- Đang tải danh sách chuyến xe --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="text-dark font-weight-bold">Chọn ghế mới:</label>
                            <div id="seatMapContainer">
                                <p class="text-muted">Vui lòng chọn chuyến xe trước</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="text-dark font-weight-bold">Phí chuyển đổi:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="transferFee" readonly>
                                <div class="input-group-append">
                                    <span class="input-group-text">VNĐ</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="change-passenger" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Tên hành khách mới:</label>
                                    <input type="text" class="form-control" id="newPassengerName" placeholder="Nhập tên hành khách">
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Số điện thoại mới:</label>
                                    <input type="tel" class="form-control" id="newPassengerPhone" placeholder="Nhập số điện thoại">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Email mới:</label>
                                    <input type="email" class="form-control" id="newPassengerEmail" placeholder="Nhập email">
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">CMND/CCCD:</label>
                                    <input type="text" class="form-control" id="newPassengerIdCard" placeholder="Nhập số CMND/CCCD">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">Lý do chuyển đổi:</label>
                    <textarea class="form-control" id="transferReason" rows="2" placeholder="Nhập lý do chuyển đổi..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-warning" id="executeTransfer">
                    <i class="fas fa-exchange-alt"></i> Thực hiện chuyển đổi
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal hoàn tiền vé -->
<div class="modal fade" id="refundModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-money-bill-wave text-success"></i>
                    Hoàn tiền vé
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="refundVeId" value="">

                <div class="alert alert-info">
                    <strong>Thông tin vé:</strong> <span id="refundTicketInfo">Đang tải...</span>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 text-dark">Thông tin thanh toán</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Giá vé gốc:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="originalPrice" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Phí hủy vé:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="cancellationFee" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Số tiền hoàn lại:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control font-weight-bold text-success" id="refundAmount" readonly>
                                        <div class="input-group-append">
                                            <span class="input-group-text">VNĐ</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0 text-dark">Phương thức hoàn tiền</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="text-dark font-weight-bold">Chọn phương thức:</label>
                                    <select class="form-control" id="refundMethod">
                                        <option value="">-- Chọn phương thức --</option>
                                        <option value="bank_transfer">Chuyển khoản ngân hàng</option>
                                        <option value="cash">Tiền mặt tại quầy</option>
                                        <option value="original_method">Hoàn về phương thức gốc</option>
                                    </select>
                                </div>
                                <div class="form-group" id="bankInfoGroup" style="display: none;">
                                    <label class="text-dark font-weight-bold">Thông tin ngân hàng:</label>
                                    <textarea class="form-control" id="bankInfo" rows="3" placeholder="Nhập số tài khoản, tên ngân hàng, tên chủ tài khoản..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label class="text-dark font-weight-bold">Lý do hoàn tiền:</label>
                    <textarea class="form-control" id="refundReason" rows="3" placeholder="Nhập lý do hoàn tiền..."></textarea>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Lưu ý:</strong> Việc hoàn tiền sẽ thay đổi trạng thái vé thành "Đã hoàn tiền" và không thể hoàn tác.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-success" id="executeRefund">
                    <i class="fas fa-money-bill-wave"></i> Thực hiện hoàn tiền
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal check-in hành khách -->
<div class="modal fade" id="checkinModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-user-check text-success"></i>
                    Check-in hành khách
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="checkinVeId" value="">

                <div class="alert alert-info">
                    <strong>Thông tin hành khách:</strong> <span id="checkinPassengerInfo">Đang tải...</span>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="text-dark font-weight-bold">
                                <i class="fas fa-clock text-primary"></i>
                                Thời gian check-in:
                            </label>
                            <input type="datetime-local" class="form-control" id="checkinTime" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="text-dark font-weight-bold">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                                Vị trí check-in:
                            </label>
                            <select class="form-control" id="checkinLocation">
                                <option value="ben_xe">Bến xe</option>
                                <option value="tren_xe">Trên xe</option>
                                <option value="diem_don">Điểm đón</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-id-card text-primary"></i>
                        Xác minh danh tính:
                    </label>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="identityVerified">
                        <label class="custom-control-label text-dark" for="identityVerified">
                            Đã xác minh CMND/CCCD của hành khách
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-thermometer-half text-primary"></i>
                        Kiểm tra sức khỏe:
                    </label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" id="healthNormal" name="healthStatus" value="normal" checked>
                                <label class="custom-control-label text-dark" for="healthNormal">
                                    <i class="fas fa-check-circle text-success"></i> Bình thường
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" id="healthConcern" name="healthStatus" value="concern">
                                <label class="custom-control-label text-dark" for="healthConcern">
                                    <i class="fas fa-exclamation-triangle text-warning"></i> Có vấn đề
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group" id="healthNoteGroup" style="display: none;">
                    <label class="text-dark font-weight-bold">Ghi chú sức khỏe:</label>
                    <textarea class="form-control" id="healthNote" rows="2" placeholder="Mô tả vấn đề sức khỏe..."></textarea>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-sticky-note text-info"></i>
                        Ghi chú check-in:
                    </label>
                    <textarea class="form-control" id="checkinNote" rows="3" placeholder="Ghi chú thêm về quá trình check-in..."></textarea>
                </div>

                <div class="alert alert-success">
                    <i class="fas fa-info-circle"></i>
                    <strong>Lưu ý:</strong> Sau khi check-in, trạng thái vé sẽ được cập nhật thành "Đã sử dụng".
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-success" id="executeCheckin">
                    <i class="fas fa-user-check"></i> Xác nhận check-in
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal tùy chọn in vé -->
<div class="modal fade" id="printOptionsModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">
                    <i class="fas fa-print text-info"></i>
                    Tùy chọn in vé
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="printVeId" value="">

                <div class="alert alert-info">
                    <strong>Vé:</strong> <span id="printTicketInfo">Đang tải...</span>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-file-alt text-primary"></i>
                        Chọn template in:
                    </label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card template-option" data-template="standard">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                    <h6 class="card-title">Template chuẩn</h6>
                                    <p class="card-text small">Template in vé truyền thống</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card template-option" data-template="modern">
                                <div class="card-body text-center">
                                    <i class="fas fa-magic fa-2x text-success mb-2"></i>
                                    <h6 class="card-title">Template hiện đại</h6>
                                    <p class="card-text small">Template đẹp với QR code</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-cogs text-primary"></i>
                        Tùy chọn bổ sung:
                    </label>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="includeQRCode" checked>
                        <label class="custom-control-label text-dark" for="includeQRCode">
                            <i class="fas fa-qrcode"></i> Bao gồm mã QR
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="includeBarcode">
                        <label class="custom-control-label text-dark" for="includeBarcode">
                            <i class="fas fa-barcode"></i> Bao gồm mã vạch
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="includeTerms" checked>
                        <label class="custom-control-label text-dark" for="includeTerms">
                            <i class="fas fa-file-contract"></i> Bao gồm điều khoản
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="includeMap">
                        <label class="custom-control-label text-dark" for="includeMap">
                            <i class="fas fa-map-marked-alt"></i> Bao gồm bản đồ tuyến đường
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-palette text-primary"></i>
                        Màu sắc template:
                    </label>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="color-option" data-color="blue" style="background: #007bff; height: 40px; border-radius: 5px; cursor: pointer; border: 2px solid transparent;" title="Xanh dương"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="color-option" data-color="green" style="background: #28a745; height: 40px; border-radius: 5px; cursor: pointer; border: 2px solid transparent;" title="Xanh lá"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="color-option" data-color="orange" style="background: #fd7e14; height: 40px; border-radius: 5px; cursor: pointer; border: 2px solid transparent;" title="Cam"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="color-option" data-color="purple" style="background: #6f42c1; height: 40px; border-radius: 5px; cursor: pointer; border: 2px solid transparent;" title="Tím"></div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="text-dark font-weight-bold">
                        <i class="fas fa-print text-primary"></i>
                        Số lượng bản in:
                    </label>
                    <select class="form-control" id="printCopies">
                        <option value="1">1 bản</option>
                        <option value="2">2 bản</option>
                        <option value="3">3 bản</option>
                        <option value="5">5 bản</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Hủy
                </button>
                <button type="button" class="btn btn-outline-info" id="previewTicket">
                    <i class="fas fa-eye"></i> Xem trước
                </button>
                <button type="button" class="btn btn-info" id="executePrint">
                    <i class="fas fa-print"></i> In vé
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal gửi thông báo hàng loạt -->
<div class="modal fade" id="bulkNotificationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-dark">Gửi thông báo hàng loạt</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <strong>Thông tin:</strong> Bạn đã chọn <span id="bulkSelectedCount">0</span> vé để gửi thông báo.
                </div>

                <!-- Template Selection -->
                <div class="form-group">
                    <label for="bulkTemplateSelect" class="text-dark font-weight-bold">Chọn mẫu thông báo</label>
                    <select class="form-control" id="bulkTemplateSelect" onchange="loadBulkTemplate()">
                        <option value="">-- Chọn mẫu thông báo --</option>
                        <option value="confirm">Xác nhận đặt vé</option>
                        <option value="payment_success">Thanh toán thành công</option>
                        <option value="departure_reminder">Nhắc nhở khởi hành</option>
                        <option value="schedule_change">Thay đổi lịch trình</option>
                        <option value="cancellation">Hủy vé</option>
                        <option value="custom">Tùy chỉnh</option>
                    </select>
                </div>

                <!-- Notification Type -->
                <div class="form-group">
                    <label class="text-dark font-weight-bold">Phương thức gửi</label>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="bulkSendEmail" checked>
                        <label class="custom-control-label text-dark" for="bulkSendEmail">
                            <i class="fas fa-envelope"></i> Email
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="bulkSendSMS">
                        <label class="custom-control-label text-dark" for="bulkSendSMS">
                            <i class="fas fa-sms"></i> SMS
                        </label>
                    </div>
                </div>

                <!-- Subject -->
                <div class="form-group">
                    <label for="bulkNotificationSubject" class="text-dark font-weight-bold">Tiêu đề</label>
                    <input type="text" class="form-control" id="bulkNotificationSubject" placeholder="Tiêu đề thông báo">
                </div>

                <!-- Message -->
                <div class="form-group">
                    <label for="bulkNotificationMessage" class="text-dark font-weight-bold">Nội dung</label>
                    <textarea class="form-control" id="bulkNotificationMessage" rows="6" placeholder="Nội dung thông báo..."></textarea>
                    <small class="form-text text-muted">
                        Có thể sử dụng các biến: {TenKhach}, {MaVe}, {SoDienThoai}, {NgayKhoiHanh}, {TuyenDuong}, {SoGhe}
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-info" id="sendBulkNotification">
                    <i class="fas fa-paper-plane"></i> Gửi thông báo
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Highlight menu item
            $('#menu-booking').addClass('active');

            // Initialize search result count
            const totalRows = $('#ticketsTable tbody tr').length;
            updateSearchResultCount(totalRows, '');
            
            // Xử lý tìm kiếm với debounce để tối ưu hiệu suất
            let searchTimeout;
            $('#searchTickets').on('keyup', function() {
                clearTimeout(searchTimeout);
                const searchValue = $(this).val().toLowerCase();

                searchTimeout = setTimeout(function() {
                    let visibleCount = 0;
                    $("#ticketsTable tbody tr").each(function() {
                        const searchData = $(this).data('search');
                        if (searchData) {
                            const isVisible = searchData.toLowerCase().indexOf(searchValue) > -1;
                            $(this).toggle(isVisible);
                            if (isVisible) visibleCount++;
                        }
                    });

                    // Update search result count
                    updateSearchResultCount(visibleCount, searchValue);
                }, 300); // Debounce 300ms
            });

            // Clear search
            $('#clearSearch').on('click', function() {
                $('#searchTickets').val('');
                $("#ticketsTable tbody tr").show();
                updateSearchResultCount($('#ticketsTable tbody tr').length, '');
            });
            
            // Xử lý modal thêm ghi chú
            $(document).on('click', '.add-note', function(e) {
                e.preventDefault();
                $('#noteVeId').val($(this).data('id'));
                $('#noteContent').val('');
                $('#noteModal').modal('show');
            });
            
            // Xử lý lưu ghi chú
            $('#saveNote').click(function() {
                var veId = $('#noteVeId').val();
                var note = $('#noteContent').val();

                if (!veId) {
                    showToast('Không tìm thấy thông tin vé', 'warning');
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("AddNoteToTicket", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        ghiChu: note
                    },
                    timeout: 10000, // 10 second timeout
                    success: function(response) {
                        if (response && response.success) {
                            showToast('Đã cập nhật ghi chú', 'success');
                            $('#noteModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể cập nhật ghi chú'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi cập nhật ghi chú';
                        if (status === 'timeout') {
                            errorMessage = 'Yêu cầu quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng này.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });
            
            // Xử lý modal gửi thông báo
            $(document).on('click', '.send-notification', function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var name = $(this).data('name');

                // Get ticket info from server
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#notificationVeId').val(veId);
                            $('#notificationSubject').val('Thông báo về vé xe của bạn');
                            $('#notificationMessage').val('Kính gửi ' + name + ',\n\nChúng tôi gửi thông báo này để...\n\nTrân trọng,\nNhà xe');
                            $('#notificationModal').modal('show');
                        } else {
                            showToast('Không thể gửi thông báo cho vé này: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Đã xảy ra lỗi khi lấy thông tin vé', 'danger');
                    }
                });
            });
            
            // Xử lý gửi thông báo
            $('#sendNotification').click(function() {
                var veId = $('#notificationVeId').val();
                var subject = $('#notificationSubject').val();
                var message = $('#notificationMessage').val();
                var sendEmail = $('#sendEmail').is(':checked');
                var sendSMS = $('#sendSMS').is(':checked');

                if (!sendEmail && !sendSMS) {
                    showToast('Vui lòng chọn ít nhất một phương thức gửi', 'warning');
                    return;
                }

                if (!subject.trim() || !message.trim()) {
                    showToast('Vui lòng nhập đầy đủ tiêu đề và nội dung', 'warning');
                    return;
                }

                // Replace placeholders with actual data
                if (window.currentCustomer) {
                    const replacements = {
                        '{TenKhach}': window.currentCustomer.tenKhach || '',
                        '{MaVe}': window.currentCustomer.maVe || '',
                        '{SoDienThoai}': window.currentCustomer.soDienThoai || '',
                        '{NgayKhoiHanh}': '[Ngày khởi hành]',
                        '{TuyenDuong}': '[Tuyến đường]',
                        '{SoGhe}': '[Số ghế]'
                    };

                    Object.keys(replacements).forEach(key => {
                        subject = subject.replace(new RegExp(key, 'g'), replacements[key]);
                        message = message.replace(new RegExp(key, 'g'), replacements[key]);
                    });
                }

                $.ajax({
                    url: '@Url.Action("SendAdvancedNotification", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        veId: veId,
                        subject: subject,
                        message: message,
                        sendEmail: sendEmail,
                        sendSMS: sendSMS
                    }),
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                            $('#notificationModal').modal('hide');

                            // Reset form
                            $('#templateSelect').val('');
                            $('#notificationSubject').val('');
                            $('#notificationMessage').val('');
                            $('#messagePreview').hide();
                        } else {
                            showToast('Lỗi: ' + response.message, 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi gửi thông báo';
                        if (status === 'timeout') {
                            errorMessage = 'Gửi thông báo quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng gửi thông báo.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi gửi thông báo. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    timeout: 30000 // 30 second timeout
                });
            });

            // Xử lý thay đổi trạng thái đơn lẻ
            $(document).on('click', '.status-change-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var veId = $(this).data('ve-id');
                var currentStatus = $(this).data('current-status');

                $('#statusChangeVeId').val(veId);
                $('#statusChangeCurrentStatus').val(currentStatus);

                // Load ticket info
                loadTicketInfoForStatusChange(veId, currentStatus);

                $('#statusChangeModal').modal('show');
            });

            // Load ticket info for status change
            function loadTicketInfoForStatusChange(veId, currentStatus) {
                // Display current status
                var statusInfo = getStatusInfo(currentStatus);
                $('#currentStatusDisplay').html(`
                    <span class="badge badge-lg" style="background-color: ${statusInfo.color}; color: white; padding: 8px 14px; border-radius: 20px;">
                        <i class="${statusInfo.icon} mr-1"></i>${statusInfo.text}
                    </span>
                `);

                // Load ticket details
                $.ajax({
                    url: '@Url.Action("GetTicketInfo", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#statusChangeTicketInfo').html(`
                                <strong>${response.maVe}</strong> - ${response.tenKhach} (${response.soDienThoai})
                            `);
                        } else {
                            $('#statusChangeTicketInfo').text('Không thể tải thông tin vé');
                        }
                    },
                    error: function() {
                        $('#statusChangeTicketInfo').text('Lỗi khi tải thông tin vé');
                    }
                });

                // Filter available status options based on current status
                filterStatusOptions(currentStatus);
            }

            // Get status info by status code
            function getStatusInfo(statusCode) {
                var statusMap = {
                    1: { text: 'Đã đặt', icon: 'fas fa-clock', color: '#ffc107' },
                    2: { text: 'Đã thanh toán', icon: 'fas fa-check-circle', color: '#28a745' },
                    3: { text: 'Đã hoàn thành', icon: 'fas fa-check-double', color: '#007bff' },
                    4: { text: 'Đã hủy', icon: 'fas fa-times-circle', color: '#dc3545' },
                    5: { text: 'Đã sử dụng', icon: 'fas fa-user-check', color: '#17a2b8' },
                    6: { text: 'Đã hoàn tiền', icon: 'fas fa-undo-alt', color: '#6c757d' }
                };
                return statusMap[statusCode] || { text: 'Không xác định', icon: 'fas fa-question-circle', color: '#6c757d' };
            }

            // Filter status options based on business rules
            function filterStatusOptions(currentStatus) {
                var validTransitions = {
                    1: [2, 4], // Đã đặt -> Đã thanh toán, Đã hủy
                    2: [3, 4, 5], // Đã thanh toán -> Đã hoàn thành, Đã hủy, Đã sử dụng
                    3: [], // Đã hoàn thành -> không thể chuyển
                    4: [6], // Đã hủy -> Đã hoàn tiền
                    5: [3], // Đã sử dụng -> Đã hoàn thành
                    6: [] // Đã hoàn tiền -> không thể chuyển
                };

                var $select = $('#newStatus');
                $select.find('option').each(function() {
                    var optionValue = parseInt($(this).val());
                    if (optionValue && optionValue !== currentStatus) {
                        if (validTransitions[currentStatus] && validTransitions[currentStatus].includes(optionValue)) {
                            $(this).show().prop('disabled', false);
                        } else {
                            $(this).hide().prop('disabled', true);
                        }
                    } else if (optionValue === currentStatus) {
                        $(this).hide().prop('disabled', true);
                    }
                });
            }

            // Handle status change validation
            $('#newStatus').change(function() {
                var newStatus = parseInt($(this).val());
                var currentStatus = parseInt($('#statusChangeCurrentStatus').val());

                $('#statusChangeWarning').hide();

                if (newStatus === 4) { // Đã hủy
                    $('#statusChangeWarning').show();
                    $('#warningMessage').text('Lưu ý: Việc hủy vé có thể ảnh hưởng đến thanh toán và hoàn tiền.');
                } else if (newStatus === 6) { // Đã hoàn tiền
                    $('#statusChangeWarning').show();
                    $('#warningMessage').text('Lưu ý: Trạng thái hoàn tiền sẽ kích hoạt quy trình hoàn tiền tự động.');
                }
            });

            // Execute status change
            $('#executeStatusChange').click(function() {
                var veId = $('#statusChangeVeId').val();
                var newStatus = $('#newStatus').val();
                var note = $('#statusChangeNote').val().trim();

                if (!newStatus) {
                    showToast('Vui lòng chọn trạng thái mới', 'warning');
                    return;
                }

                if (!confirm('Xác nhận thay đổi trạng thái vé?')) {
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang cập nhật...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("UpdateTicketStatus", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        trangThai: getStatusEnumName(newStatus),
                        ghiChu: note,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response && response.success) {
                            showToast(response.message || 'Cập nhật trạng thái thành công', 'success');
                            $('#statusChangeModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể cập nhật trạng thái'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi cập nhật trạng thái';
                        if (status === 'timeout') {
                            errorMessage = 'Cập nhật quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng cập nhật trạng thái.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi cập nhật. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Get status enum name from number
            function getStatusEnumName(statusNumber) {
                var statusMap = {
                    1: 'DaDat',
                    2: 'DaThanhToan',
                    3: 'DaHoanThanh',
                    4: 'DaHuy',
                    5: 'DaSuDung',
                    6: 'DaHoanTien'
                };
                return statusMap[statusNumber] || 'DaDat';
            }

            // Xử lý bulk update
            $('#executeBulkUpdate').click(function() {
                var checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
                var veIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));
                var status = $('#bulkStatus').val();
                var note = $('#bulkNote').val().trim();

                if (veIds.length === 0) {
                    showToast('Vui lòng chọn ít nhất một vé', 'warning');
                    return;
                }

                if (!status) {
                    showToast('Vui lòng chọn trạng thái mới', 'warning');
                    return;
                }

                // Confirm action
                if (!confirm(`Xác nhận cập nhật trạng thái cho ${veIds.length} vé?`)) {
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang cập nhật...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("BulkUpdateStatus", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    timeout: 15000, // 15 second timeout
                    data: JSON.stringify({
                        VeIds: veIds,
                        TrangThai: status,
                        GhiChu: note
                    }),
                    success: function(response) {
                        if (response && response.success) {
                            showToast(response.message || 'Cập nhật thành công', 'success');
                            $('#bulkUpdateModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể cập nhật'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi cập nhật hàng loạt';
                        if (status === 'timeout') {
                            errorMessage = 'Cập nhật quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng cập nhật.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi cập nhật. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Xử lý hoàn tiền vé
            $(document).on('click', '.refund-ticket', function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var maVe = $(this).data('ma-ve');

                $('#refundVeId').val(veId);
                $('#refundTicketInfo').text('Mã vé: ' + maVe);

                // Load refund calculation
                loadRefundCalculation(veId);

                $('#refundModal').modal('show');
            });

            // Load refund calculation
            function loadRefundCalculation(veId) {
                $.ajax({
                    url: '@Url.Action("CalculateRefund", "Booking")',
                    type: 'GET',
                    data: { veId: veId },
                    success: function(response) {
                        if (response.success) {
                            $('#originalPrice').val(formatCurrency(response.originalPrice));
                            $('#cancellationFee').val(formatCurrency(response.cancellationFee));
                            $('#refundAmount').val(formatCurrency(response.refundAmount));
                        } else {
                            showToast('Không thể tính toán hoàn tiền: ' + response.message, 'warning');
                        }
                    },
                    error: function() {
                        showToast('Lỗi khi tính toán hoàn tiền', 'danger');
                    }
                });
            }

            // Handle refund method change
            $('#refundMethod').change(function() {
                if ($(this).val() === 'bank_transfer') {
                    $('#bankInfoGroup').show();
                } else {
                    $('#bankInfoGroup').hide();
                }
            });

            // Execute refund
            $('#executeRefund').click(function() {
                var veId = $('#refundVeId').val();
                var method = $('#refundMethod').val();
                var reason = $('#refundReason').val().trim();
                var bankInfo = $('#bankInfo').val().trim();

                if (!method) {
                    showToast('Vui lòng chọn phương thức hoàn tiền', 'warning');
                    return;
                }

                if (!reason) {
                    showToast('Vui lòng nhập lý do hoàn tiền', 'warning');
                    return;
                }

                if (method === 'bank_transfer' && !bankInfo) {
                    showToast('Vui lòng nhập thông tin ngân hàng', 'warning');
                    return;
                }

                if (!confirm('Xác nhận thực hiện hoàn tiền? Thao tác này không thể hoàn tác.')) {
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("ProcessRefund", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        method: method,
                        reason: reason,
                        bankInfo: bankInfo,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response && response.success) {
                            showToast(response.message || 'Hoàn tiền thành công', 'success');
                            $('#refundModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể thực hiện hoàn tiền'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi hoàn tiền';
                        if (status === 'timeout') {
                            errorMessage = 'Hoàn tiền quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng hoàn tiền.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi hoàn tiền. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Format currency
            function formatCurrency(amount) {
                return new Intl.NumberFormat('vi-VN').format(amount);
            }

            // Xử lý chuyển vé
            $(document).on('click', '.transfer-ticket', function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var maVe = $(this).data('ma-ve');

                $('#transferVeId').val(veId);
                $('#transferTicketInfo').text('Mã vé: ' + maVe);

                // Load available trips
                loadAvailableTrips(veId);

                $('#transferModal').modal('show');
            });

            // Load available trips for transfer
            function loadAvailableTrips(veId) {
                $.ajax({
                    url: '@Url.Action("GetAvailableTrips", "Booking")',
                    type: 'GET',
                    data: { excludeVeId: veId },
                    success: function(response) {
                        if (response.success) {
                            var options = '<option value="">-- Chọn chuyến xe --</option>';
                            response.trips.forEach(function(trip) {
                                options += `<option value="${trip.id}" data-price="${trip.price}">
                                    ${trip.route} - ${trip.departureTime} (${formatCurrency(trip.price)} VNĐ)
                                </option>`;
                            });
                            $('#newTripSelect').html(options);
                        } else {
                            $('#newTripSelect').html('<option value="">Không có chuyến xe khả dụng</option>');
                        }
                    },
                    error: function() {
                        $('#newTripSelect').html('<option value="">Lỗi khi tải danh sách chuyến xe</option>');
                    }
                });
            }

            // Handle trip selection change
            $('#newTripSelect').change(function() {
                var tripId = $(this).val();
                if (tripId) {
                    loadSeatMap(tripId);
                    calculateTransferFee();
                } else {
                    $('#seatMapContainer').html('<p class="text-muted">Vui lòng chọn chuyến xe trước</p>');
                    $('#transferFee').val('0');
                }
            });

            // Load seat map for selected trip
            function loadSeatMap(tripId) {
                $('#seatMapContainer').html('<p class="text-info">Đang tải sơ đồ ghế...</p>');

                $.ajax({
                    url: '@Url.Action("GetSeatMap", "Booking")',
                    type: 'GET',
                    data: { tripId: tripId },
                    success: function(response) {
                        if (response.success) {
                            $('#seatMapContainer').html(response.seatMapHtml);
                        } else {
                            $('#seatMapContainer').html('<p class="text-danger">Không thể tải sơ đồ ghế</p>');
                        }
                    },
                    error: function() {
                        $('#seatMapContainer').html('<p class="text-danger">Lỗi khi tải sơ đồ ghế</p>');
                    }
                });
            }

            // Calculate transfer fee
            function calculateTransferFee() {
                var selectedOption = $('#newTripSelect option:selected');
                var newPrice = parseFloat(selectedOption.data('price')) || 0;
                var originalPrice = parseFloat($('#originalPrice').val().replace(/,/g, '')) || 0;

                var fee = Math.max(0, newPrice - originalPrice);
                $('#transferFee').val(formatCurrency(fee));
            }

            // Xử lý check-in hành khách
            $(document).on('click', '.check-in-passenger', function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var maVe = $(this).data('ma-ve');
                var tenKhach = $(this).data('ten-khach');

                $('#checkinVeId').val(veId);
                $('#checkinPassengerInfo').text(`${maVe} - ${tenKhach}`);

                // Set current time
                var now = new Date();
                var localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
                $('#checkinTime').val(localDateTime);

                $('#checkinModal').modal('show');
            });

            // Handle health status change
            $('input[name="healthStatus"]').change(function() {
                if ($(this).val() === 'concern') {
                    $('#healthNoteGroup').show();
                } else {
                    $('#healthNoteGroup').hide();
                    $('#healthNote').val('');
                }
            });

            // Execute check-in
            $('#executeCheckin').click(function() {
                var veId = $('#checkinVeId').val();
                var checkinTime = $('#checkinTime').val();
                var location = $('#checkinLocation').val();
                var identityVerified = $('#identityVerified').is(':checked');
                var healthStatus = $('input[name="healthStatus"]:checked').val();
                var healthNote = $('#healthNote').val().trim();
                var note = $('#checkinNote').val().trim();

                if (!identityVerified) {
                    showToast('Vui lòng xác minh danh tính hành khách', 'warning');
                    return;
                }

                if (healthStatus === 'concern' && !healthNote) {
                    showToast('Vui lòng ghi chú vấn đề sức khỏe', 'warning');
                    return;
                }

                if (!confirm('Xác nhận check-in hành khách? Trạng thái vé sẽ được cập nhật thành "Đã sử dụng".')) {
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang xử lý...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("CheckInPassenger", "Booking")',
                    type: 'POST',
                    data: {
                        veId: veId,
                        checkinTime: checkinTime,
                        location: location,
                        identityVerified: identityVerified,
                        healthStatus: healthStatus,
                        healthNote: healthNote,
                        note: note,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response && response.success) {
                            showToast(response.message || 'Check-in thành công', 'success');
                            $('#checkinModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể check-in'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi check-in';
                        if (status === 'timeout') {
                            errorMessage = 'Check-in quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng check-in.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi check-in. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Xử lý tùy chọn in vé
            $(document).on('click', '.print-ticket-options', function(e) {
                e.preventDefault();
                var veId = $(this).data('id');
                var maVe = $(this).data('ma-ve');

                $('#printVeId').val(veId);
                $('#printTicketInfo').text(`Mã vé: ${maVe}`);

                // Reset selections
                $('.template-option').removeClass('selected');
                $('.color-option').removeClass('selected');
                $('.template-option[data-template="standard"]').addClass('selected');
                $('.color-option[data-color="blue"]').addClass('selected');

                $('#printOptionsModal').modal('show');
            });

            // Handle template selection
            $('.template-option').click(function() {
                $('.template-option').removeClass('selected');
                $(this).addClass('selected');
            });

            // Handle color selection
            $('.color-option').click(function() {
                $('.color-option').removeClass('selected');
                $(this).addClass('selected');
                $(this).css('border', '2px solid #333');
                $('.color-option').not(this).css('border', '2px solid transparent');
            });

            // Preview ticket
            $('#previewTicket').click(function() {
                var printOptions = getPrintOptions();
                if (!printOptions) return;

                // Show loading
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang tạo...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("PreviewTicket", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(printOptions),
                    success: function(response) {
                        if (response && response.success) {
                            // Open preview in new window
                            var previewWindow = window.open('', '_blank', 'width=800,height=600');
                            previewWindow.document.write(response.html);
                            previewWindow.document.close();
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể tạo bản xem trước'), 'danger');
                        }
                    },
                    error: function() {
                        showToast('Có lỗi xảy ra khi tạo bản xem trước', 'danger');
                    },
                    complete: function() {
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Execute print
            $('#executePrint').click(function() {
                var printOptions = getPrintOptions();
                if (!printOptions) return;

                // Show loading
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang in...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("PrintTicketAdvanced", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(printOptions),
                    success: function(response) {
                        if (response && response.success) {
                            showToast('Chuẩn bị in vé thành công', 'success');

                            // Open print window
                            var printWindow = window.open('', '_blank', 'width=800,height=600');
                            printWindow.document.write(response.html);
                            printWindow.document.close();

                            // Auto print after a short delay
                            setTimeout(() => {
                                try {
                                    printWindow.print();
                                } catch (e) {
                                    showToast('Không thể in tự động. Vui lòng sử dụng Ctrl+P.', 'info');
                                }
                            }, 1000);

                            $('#printOptionsModal').modal('hide');
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể in vé'), 'danger');
                        }
                    },
                    error: function() {
                        showToast('Có lỗi xảy ra khi in vé', 'danger');
                    },
                    complete: function() {
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Get print options
            function getPrintOptions() {
                var veId = $('#printVeId').val();
                var template = $('.template-option.selected').data('template');
                var color = $('.color-option.selected').data('color');
                var copies = $('#printCopies').val();

                if (!veId) {
                    showToast('Không tìm thấy thông tin vé', 'warning');
                    return null;
                }

                if (!template) {
                    showToast('Vui lòng chọn template', 'warning');
                    return null;
                }

                if (!color) {
                    showToast('Vui lòng chọn màu sắc', 'warning');
                    return null;
                }

                return {
                    veId: veId,
                    template: template,
                    color: color,
                    copies: parseInt(copies),
                    includeQRCode: $('#includeQRCode').is(':checked'),
                    includeBarcode: $('#includeBarcode').is(':checked'),
                    includeTerms: $('#includeTerms').is(':checked'),
                    includeMap: $('#includeMap').is(':checked')
                };
            }

            // Xử lý gửi thông báo hàng loạt
            $('#sendBulkNotification').click(function() {
                var checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
                var veIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));
                var subject = $('#bulkNotificationSubject').val().trim();
                var message = $('#bulkNotificationMessage').val().trim();
                var sendEmail = $('#bulkSendEmail').is(':checked');
                var sendSMS = $('#bulkSendSMS').is(':checked');

                if (veIds.length === 0) {
                    showToast('Vui lòng chọn ít nhất một vé', 'warning');
                    return;
                }

                if (!sendEmail && !sendSMS) {
                    showToast('Vui lòng chọn ít nhất một phương thức gửi', 'warning');
                    return;
                }

                if (!subject) {
                    showToast('Vui lòng nhập tiêu đề thông báo', 'warning');
                    $('#bulkNotificationSubject').focus();
                    return;
                }

                if (!message) {
                    showToast('Vui lòng nhập nội dung thông báo', 'warning');
                    $('#bulkNotificationMessage').focus();
                    return;
                }

                // Confirm before sending
                if (!confirm(`Xác nhận gửi thông báo cho ${veIds.length} vé?`)) {
                    return;
                }

                // Show loading state
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.html('<i class="fas fa-spinner fa-spin"></i> Đang gửi...').prop('disabled', true);

                $.ajax({
                    url: '@Url.Action("BulkSendNotification", "Booking")',
                    type: 'POST',
                    contentType: 'application/json',
                    timeout: 60000, // 60 second timeout for bulk email sending
                    data: JSON.stringify({
                        VeIds: veIds,
                        Subject: subject,
                        Message: message,
                        SendEmail: sendEmail,
                        SendSMS: sendSMS
                    }),
                    success: function(response) {
                        if (response && response.success) {
                            showToast(response.message || 'Đã gửi thông báo thành công', 'success');
                            $('#bulkNotificationModal').modal('hide');

                            // Reset form
                            $('#bulkTemplateSelect').val('');
                            $('#bulkNotificationSubject').val('');
                            $('#bulkNotificationMessage').val('');
                        } else {
                            showToast('Lỗi: ' + (response.message || 'Không thể gửi thông báo'), 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMessage = 'Đã xảy ra lỗi khi gửi thông báo hàng loạt';
                        if (status === 'timeout') {
                            errorMessage = 'Gửi thông báo quá thời gian chờ. Vui lòng thử lại.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Không tìm thấy chức năng gửi thông báo.';
                        } else if (xhr.status === 500) {
                            errorMessage = 'Lỗi máy chủ khi gửi thông báo. Vui lòng thử lại sau.';
                        }
                        showToast(errorMessage, 'danger');
                    },
                    complete: function() {
                        // Restore button state
                        $btn.html(originalText).prop('disabled', false);
                    }
                });
            });
        });

        function updateTripStatus(veId, status) {
            // Validation
            if (!veId || !status) {
                showToast('Thông tin không hợp lệ', 'warning');
                return;
            }

            let statusText = '';
            switch(status) {
                case 'DaDon': statusText = 'đã đón'; break;
                case 'KhongCoMat': statusText = 'không có mặt'; break;
                case 'HuyChuyen': statusText = 'huỷ chuyến'; break;
                default:
                    showToast('Trạng thái không hợp lệ', 'warning');
                    return;
            }

            if (!confirm(`Xác nhận cập nhật trạng thái ${statusText} cho vé này?`)) return;

            // Show loading toast
            showToast('Đang cập nhật trạng thái...', 'info');

            fetch('@Url.Action("UpdateTripStatus", "Booking")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `veId=${encodeURIComponent(veId)}&trangThai=${encodeURIComponent(status)}`
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(res => {
                if (res && res.success) {
                    showToast(res.message || 'Cập nhật trạng thái thành công', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showToast(res.message || 'Không thể cập nhật trạng thái', 'danger');
                }
            })
            .catch(err => {
                let errorMessage = 'Có lỗi xảy ra khi cập nhật trạng thái';
                if (err.message.includes('404')) {
                    errorMessage = 'Không tìm thấy chức năng cập nhật trạng thái';
                } else if (err.message.includes('500')) {
                    errorMessage = 'Lỗi máy chủ. Vui lòng thử lại sau';
                } else if (err.message.includes('NetworkError') || err.message.includes('Failed to fetch')) {
                    errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối';
                }
                showToast(errorMessage, 'danger');
                console.error('UpdateTripStatus error:', err);
            });
        }

        // Bulk update functions
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const ticketCheckboxes = document.querySelectorAll('.ticket-checkbox');

            ticketCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const bulkBtn = document.getElementById('bulkUpdateBtn');
            const bulkNotificationBtn = document.getElementById('bulkNotificationBtn');
            const bulkPrintBtn = document.getElementById('bulkPrintBtn');

            if (checkedBoxes.length > 0) {
                bulkBtn.disabled = false;
                bulkBtn.innerHTML = `<i class="fas fa-edit"></i> Cập nhật ${checkedBoxes.length} vé`;

                bulkNotificationBtn.disabled = false;
                bulkNotificationBtn.innerHTML = `<i class="fas fa-envelope"></i> Gửi thông báo cho ${checkedBoxes.length} vé`;

                bulkPrintBtn.disabled = false;
                bulkPrintBtn.innerHTML = `<i class="fas fa-print"></i> In ${checkedBoxes.length} vé`;
            } else {
                bulkBtn.disabled = true;
                bulkBtn.innerHTML = '<i class="fas fa-edit"></i> Cập nhật hàng loạt';

                bulkNotificationBtn.disabled = true;
                bulkNotificationBtn.innerHTML = '<i class="fas fa-envelope"></i> Gửi thông báo hàng loạt';

                bulkPrintBtn.disabled = true;
                bulkPrintBtn.innerHTML = '<i class="fas fa-print"></i> In vé hàng loạt';
            }
        }

        function showBulkUpdateModal() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const count = checkedBoxes.length;

            if (count === 0) {
                showToast('Vui lòng chọn ít nhất một vé', 'warning');
                return;
            }

            document.getElementById('selectedCount').textContent = count;
            $('#bulkUpdateModal').modal('show');
        }

        function showBulkNotificationModal() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const count = checkedBoxes.length;

            if (count === 0) {
                showToast('Vui lòng chọn ít nhất một vé', 'warning');
                return;
            }

            document.getElementById('bulkSelectedCount').textContent = count;
            $('#bulkNotificationModal').modal('show');
        }

        function bulkPrintTickets() {
            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const veIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));

            if (veIds.length === 0) {
                showToast('Vui lòng chọn ít nhất một vé', 'warning');
                return;
            }

            // Confirm before printing
            if (!confirm(`Xác nhận in ${veIds.length} vé?`)) {
                return;
            }

            showToast('Đang chuẩn bị vé để in...', 'info');

            $.ajax({
                url: '@Url.Action("BulkPrintTickets", "Booking")',
                type: 'POST',
                contentType: 'application/json',
                timeout: 30000, // 30 second timeout
                data: JSON.stringify({
                    VeIds: veIds
                }),
                success: function(response) {
                    if (response && response.success) {
                        showToast(response.message || 'Chuẩn bị in vé thành công', 'success');

                        // Validate HTML content
                        if (!response.html || response.html.trim() === '') {
                            showToast('Không có nội dung vé để in', 'warning');
                            return;
                        }

                        try {
                            // Open print window with the HTML content
                            const printWindow = window.open('', '_blank', 'width=800,height=600');
                            if (!printWindow) {
                                showToast('Không thể mở cửa sổ in. Vui lòng cho phép popup.', 'warning');
                                return;
                            }

                            printWindow.document.write(response.html);
                            printWindow.document.close();

                            // Auto print after a short delay
                            setTimeout(() => {
                                try {
                                    printWindow.print();
                                } catch (e) {
                                    showToast('Không thể in tự động. Vui lòng sử dụng Ctrl+P.', 'info');
                                }
                            }, 1000);
                        } catch (e) {
                            showToast('Lỗi khi mở cửa sổ in: ' + e.message, 'danger');
                        }
                    } else {
                        showToast('Lỗi: ' + (response.message || 'Không thể chuẩn bị vé để in'), 'danger');
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = 'Đã xảy ra lỗi khi chuẩn bị in vé';
                    if (status === 'timeout') {
                        errorMessage = 'Chuẩn bị in vé quá thời gian chờ. Vui lòng thử lại.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Không tìm thấy chức năng in vé.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'Lỗi máy chủ khi chuẩn bị in vé. Vui lòng thử lại sau.';
                    }
                    showToast(errorMessage, 'danger');
                }
            });
        }

        // Advanced filter functions
        function toggleAdvancedFilter() {
            const advancedFilter = document.getElementById('advancedFilter');
            const isVisible = advancedFilter.style.display !== 'none';

            if (isVisible) {
                advancedFilter.style.display = 'none';
            } else {
                advancedFilter.style.display = 'block';
            }
        }

        function exportWithFilters(format = 'csv') {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            formData.append('format', format);
            const params = new URLSearchParams(formData);

            // Show loading
            showToast('Đang chuẩn bị file xuất...', 'info');

            // Create a temporary link to download
            const url = '@Url.Action("Export", "Booking")?' + params.toString();
            window.open(url, '_blank');
        }

        // Hiển thị modal gửi thông báo
        function showNotificationModal(veId) {
            $('#notificationVeId').val(veId);

            // Load customer info
            $.get('@Url.Action("GetTicketInfo", "Booking")', { veId: veId })
                .done(function(response) {
                    if (response.success) {
                        $('#customerInfo').html(`
                            <strong>${response.tenKhach}</strong><br>
                            Email: ${response.email}<br>
                            SĐT: ${response.soDienThoai}<br>
                            Mã vé: ${response.maVe}
                        `);

                        // Store customer data for template replacement
                        window.currentCustomer = response;
                    } else {
                        $('#customerInfo').text('Không thể tải thông tin khách hàng');
                    }
                })
                .fail(function() {
                    $('#customerInfo').text('Lỗi khi tải thông tin khách hàng');
                });

            $('#notificationModal').modal('show');
        }

        // Load notification templates
        function loadTemplate() {
            const template = $('#templateSelect').val();
            const templates = {
                confirm: {
                    subject: 'Xác nhận đặt vé thành công - {MaVe}',
                    message: `Kính chào {TenKhach},

Cảm ơn quý khách đã đặt vé tại hệ thống của chúng tôi.

Thông tin vé:
- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                payment_success: {
                    subject: 'Thanh toán thành công - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xác nhận đã nhận được thanh toán cho vé {MaVe}.

Thông tin chuyến đi:
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vé của quý khách đã được kích hoạt. Vui lòng mang theo giấy tờ tùy thân khi đi xe.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                departure_reminder: {
                    subject: 'Nhắc nhở: Chuyến xe của quý khách sắp khởi hành',
                    message: `Kính chào {TenKhach},

Đây là thông báo nhắc nhở về chuyến xe của quý khách:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Chúc quý khách có chuyến đi an toàn!

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                schedule_change: {
                    subject: 'Thông báo thay đổi lịch trình - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo có sự thay đổi về lịch trình chuyến xe:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Thời gian mới: [Vui lòng cập nhật thời gian mới]

Chúng tôi xin lỗi vì sự bất tiện này. Vui lòng liên hệ hotline để được hỗ trợ thêm.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                cancellation: {
                    subject: 'Thông báo hủy vé - {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo vé {MaVe} đã được hủy theo yêu cầu.

Thông tin vé đã hủy:
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ hotline để được hỗ trợ.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                }
            };

            if (templates[template]) {
                $('#notificationSubject').val(templates[template].subject);
                $('#notificationMessage').val(templates[template].message);
            } else if (template === 'custom') {
                $('#notificationSubject').val('');
                $('#notificationMessage').val('');
            }
        }

        // Preview message with customer data
        function previewMessage() {
            if (!window.currentCustomer) {
                showToast('Chưa có thông tin khách hàng để xem trước', 'warning');
                return;
            }

            let subject = $('#notificationSubject').val();
            let message = $('#notificationMessage').val();

            // Replace placeholders with actual data
            const replacements = {
                '{TenKhach}': window.currentCustomer.tenKhach || '',
                '{MaVe}': window.currentCustomer.maVe || '',
                '{SoDienThoai}': window.currentCustomer.soDienThoai || '',
                '{NgayKhoiHanh}': '[Ngày khởi hành]',
                '{TuyenDuong}': '[Tuyến đường]',
                '{SoGhe}': '[Số ghế]'
            };

            Object.keys(replacements).forEach(key => {
                subject = subject.replace(new RegExp(key, 'g'), replacements[key]);
                message = message.replace(new RegExp(key, 'g'), replacements[key]);
            });

            $('#previewContent').html(`
                <strong>Tiêu đề:</strong> ${subject}<br><br>
                <strong>Nội dung:</strong><br>
                <div style="white-space: pre-line; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px;">
                    ${message}
                </div>
            `);
            $('#messagePreview').show();
        }

        // Update search result count
        function updateSearchResultCount(count, searchTerm) {
            const resultElement = document.getElementById('searchResultCount');
            if (searchTerm) {
                resultElement.textContent = `Tìm thấy ${count} kết quả cho "${searchTerm}"`;
                resultElement.className = 'text-info';
            } else {
                resultElement.textContent = `Hiển thị ${count} vé`;
                resultElement.className = 'text-muted';
            }
        }

        // Advanced filter with validation
        function validateAndSubmitFilter() {
            const form = document.getElementById('filterForm');
            const tuNgay = form.tuNgay.value;
            const denNgay = form.denNgay.value;
            const giaVeMin = parseFloat(form.giaVeMin.value) || 0;
            const giaVeMax = parseFloat(form.giaVeMax.value) || 0;

            // Validate date range
            if (tuNgay && denNgay && new Date(tuNgay) > new Date(denNgay)) {
                showToast('Ngày bắt đầu không thể lớn hơn ngày kết thúc', 'warning');
                return false;
            }

            // Validate price range
            if (giaVeMax > 0 && giaVeMin > giaVeMax) {
                showToast('Giá vé từ không thể lớn hơn giá vé đến', 'warning');
                return false;
            }

            // Show loading
            showToast('Đang tìm kiếm...', 'info');

            // Submit form
            form.submit();
            return true;
        }

        // Reset all filters
        function resetAllFilters() {
            if (confirm('Xác nhận đặt lại tất cả bộ lọc?')) {
                window.location.href = '@Url.Action("AdminBookingList", "Booking")';
            }
        }

        // Quick filter functions
        function quickFilterByStatus(status) {
            const form = document.getElementById('filterForm');
            form.trangThaiVe.value = status;
            validateAndSubmitFilter();
        }

        function quickFilterByPaymentStatus(isPaid) {
            const form = document.getElementById('filterForm');
            form.trangThaiThanhToan.value = isPaid;
            validateAndSubmitFilter();
        }

        function quickFilterByDateRange(days) {
            const form = document.getElementById('filterForm');
            const today = new Date();
            const fromDate = new Date(today);
            fromDate.setDate(today.getDate() - days);

            form.tuNgay.value = fromDate.toISOString().split('T')[0];
            form.denNgay.value = today.toISOString().split('T')[0];
            validateAndSubmitFilter();
        }

        // Load bulk notification templates
        function loadBulkTemplate() {
            const template = $('#bulkTemplateSelect').val();
            const templates = {
                confirm: {
                    subject: 'Xác nhận đặt vé thành công - {MaVe}',
                    message: `Kính chào {TenKhach},

Cảm ơn quý khách đã đặt vé thành công.

Thông tin vé:
- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}
- Giá vé: {GiaVe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                payment_success: {
                    subject: 'Thanh toán thành công - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xác nhận đã nhận được thanh toán cho vé xe của quý khách.

Thông tin vé:
- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}
- Giá vé: {GiaVe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                departure_reminder: {
                    subject: 'Nhắc nhở: Chuyến xe của quý khách sắp khởi hành',
                    message: `Kính chào {TenKhach},

Đây là thông báo nhắc nhở về chuyến xe của quý khách:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Vui lòng có mặt tại bến xe trước giờ khởi hành 15 phút.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                schedule_change: {
                    subject: 'Thông báo thay đổi lịch trình - Vé {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo có sự thay đổi về lịch trình chuyến xe:

- Mã vé: {MaVe}
- Tuyến đường: {TuyenDuong}
- Thời gian mới: [Vui lòng cập nhật thời gian mới]

Xin lỗi vì sự bất tiện này.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                },
                cancellation: {
                    subject: 'Thông báo hủy vé - {MaVe}',
                    message: `Kính chào {TenKhach},

Chúng tôi xin thông báo vé {MaVe} đã được hủy theo yêu cầu.

Thông tin vé đã hủy:
- Tuyến đường: {TuyenDuong}
- Ngày khởi hành: {NgayKhoiHanh}
- Số ghế: {SoGhe}

Nếu có bất kỳ thắc mắc nào, vui lòng liên hệ hotline để được hỗ trợ.

Trân trọng,
Đội ngũ hỗ trợ khách hàng`
                }
            };

            if (templates[template]) {
                $('#bulkNotificationSubject').val(templates[template].subject);
                $('#bulkNotificationMessage').val(templates[template].message);
            } else if (template === 'custom') {
                $('#bulkNotificationSubject').val('');
                $('#bulkNotificationMessage').val('');
            }
        }

        // Hiển thị thông báo toast
        function showToast(message, type) {
            var toast = $('<div class="toast align-items-center text-white bg-' + type + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">');
            toast.html(
                '<div class="d-flex">'+
                '  <div class="toast-body">' + message + '</div>'+
                '  <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>'+
                '</div>'
            );
            
            $('.toast-container').append(toast);
            var bsToast = new bootstrap.Toast(toast, { delay: 3000 });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>
    
    <style>
        .avatar-sm {
            height: 3rem;
            width: 3rem;
            font-size: 1.25rem;
        }

        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.75em;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
        }

        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            border: none;
            transition: transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }

        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .table th, .table td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
            border-color: #dee2e6;
        }

        .table th {
            font-weight: 600;
            background-color: #f8f9fa;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0,123,255,.05);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .dropdown-item i {
            width: 1rem;
            text-align: center;
            margin-right: 0.5rem;
        }

        #searchTickets {
            border-radius: 20px;
            padding-left: 2.5rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236c757d' class='bi bi-search' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: 10px center;
            background-size: 16px;
            border: 1px solid #ced4da;
            transition: all 0.2s;
        }

        #searchTickets:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .info-box {
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            transition: transform 0.2s;
        }

        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }

        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .badge-lg {
            font-size: 0.9rem;
            padding: 8px 12px;
            font-weight: bold;
            border-radius: 0.375rem;
        }

        .form-control {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            transition: all 0.2s;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .modal-content {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,.2);
        }

        .modal-header {
            border-bottom: 1px solid #dee2e6;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .text-dark {
            color: #212529 !important;
        }

        .font-weight-bold {
            font-weight: 600 !important;
        }

        /* Enhanced ticket status styles */
        .ticket-status-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .ticket-status-enhanced {
            position: relative;
            display: inline-flex;
            align-items: center;
            transition: all 0.3s ease;
            cursor: default;
            min-width: 120px;
            justify-content: center;
        }

        .ticket-status-enhanced:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
        }

        .ticket-status-pending {
            animation: pulse-warning 2s infinite;
        }

        .ticket-status-paid {
            position: relative;
            overflow: hidden;
        }

        .ticket-status-paid::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 3s infinite;
        }

        .ticket-status-completed {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }

        .ticket-status-cancelled {
            position: relative;
        }

        .ticket-status-cancelled::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255,255,255,0.8);
            transform: translateY(-50%);
        }

        .ticket-status-used {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
        }

        .ticket-status-refunded {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
        }

        .status-quick-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .ticket-status-container:hover .status-quick-actions {
            opacity: 1;
        }

        .btn-xs {
            padding: 0.15rem 0.3rem;
            font-size: 0.7rem;
            border-radius: 0.2rem;
        }

        .status-change-btn {
            border: 1px solid #dee2e6;
            background: white;
            color: #6c757d;
        }

        .status-change-btn:hover {
            background: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        @@keyframes pulse-warning {
            0% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 2px 8px rgba(255,193,7,0.4); }
            100% { box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        }

        @@keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Status change dropdown */
        .status-dropdown {
            min-width: 200px;
        }

        .status-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
        }

        .status-option i {
            width: 20px;
            margin-right: 8px;
        }

        /* Print options modal styles */
        .template-option {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            margin-bottom: 15px;
        }

        .template-option:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .template-option.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .color-option {
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.selected {
            border: 2px solid #333 !important;
            transform: scale(1.1);
        }

        .print-preview {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
    </style>
}
