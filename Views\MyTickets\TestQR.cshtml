@{
    ViewData["Title"] = "Test QR Code";
}

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4>Test QR Code Scanner</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>QR Code Sample</h5>
                            <div class="text-center mb-3">
                                <img src="@Url.Action("QRCode", new { id = 1 })" 
                                     alt="Sample QR Code" class="img-fluid" style="max-width: 200px;" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5>QR Code Scanner</h5>
                            <div class="mb-3">
                                <label for="qrInput" class="form-label">Paste QR Code Data:</label>
                                <textarea class="form-control" id="qrInput" rows="5" 
                                          placeholder="Paste the QR code data here..."></textarea>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="parseQRData()">
                                Parse QR Data
                            </button>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h5>Parsed Data:</h5>
                        <div id="parsedData" class="alert alert-info" style="display: none;">
                            <pre id="dataContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function parseQRData() {
    const qrData = document.getElementById('qrInput').value.trim();
    if (!qrData) {
        alert('Please enter QR code data');
        return;
    }
    
    try {
        // Try to parse as JSON first
        const parsed = JSON.parse(qrData);
        document.getElementById('dataContent').textContent = JSON.stringify(parsed, null, 2);
        document.getElementById('parsedData').style.display = 'block';
    } catch (e) {
        // If not JSON, display as plain text
        document.getElementById('dataContent').textContent = qrData;
        document.getElementById('parsedData').style.display = 'block';
    }
}
</script>
